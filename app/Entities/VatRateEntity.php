<?php

namespace App\Entities;

class VatRateEntity extends BaseEntity
{
    public static string $table = 'vat_rates';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'archived_at',
        'deleted_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'rate',
        'description',
        'is_default',
    ];

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }
}
