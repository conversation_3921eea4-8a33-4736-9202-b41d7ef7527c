<?php

namespace App\Entities;

class WarehouseEntity extends BaseEntity
{
    public static string $table = 'warehouses';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'archived_at',
        'name',
        'cabinet_id',
        'work_schedule_id',
        'control_free_residuals',
        'responsible_employee_id',
        'address_id',
        'phone_id',
        'employee_id',
        'is_common',
        'is_default',
        'department_id',
        'group_id'
    ];

    public function warehouse_addresses(): RelationBuilder
    {
        return $this->hasOne(WarehouseAddressEntity::class, 'address_id', 'id');
    }

    public function warehouse_groups(): RelationBuilder
    {
        return $this->hasOne(WarehouseGroupEntity::class, 'group_id', 'id');
    }

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function responsible_employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'responsible_employee_id', 'id');
    }
}
