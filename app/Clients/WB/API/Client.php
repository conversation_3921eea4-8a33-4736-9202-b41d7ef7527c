<?php

declare(strict_types=1);

namespace App\Clients\WB\API;

use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Query;
use InvalidArgumentException;
use JsonException;

/**
 * HTTP клиент для работы с API Wildberries
 *
 * Класс обеспечивает базовую функциональность для выполнения HTTP-запросов к API Wildberries,
 * включая обработку ошибок, заголовков и ответов.
 */
class Client
{
    /** @var int Код ответа HTTP */
    public int $responseCode = 0;

    /** @var string|null Текстовое описание кода ответа */
    public ?string $responsePhrase = null;

    /** @var array Заголовки ответа */
    public array $responseHeaders = [];

    /** @var string|null Сырое содержимое ответа */
    public ?string $rawResponse = null;

    /** @var mixed Обработанный ответ (объект или строка) */
    public mixed $response = null;

    /** @var int Максимальное количество запросов в единицу времени */
    public int $rateLimit = 0;

    /** @var int Оставшееся количество запросов */
    public int $rateRemaining = 0;

    /** @var int Время сброса ограничения в секундах */
    public int $rateReset = 0;

    /** @var int Рекомендуемое время ожидания перед повторным запросом в миллисекундах */
    public int $rateRetry = 0;

    /** @var string Базовый URL API */
    private string $baseUrl;

    /** @var string API ключ */
    private string $apiKey;

    /** @var HttpClient Экземпляр HTTP клиента Guzzle */
    private HttpClient $Client;

    /** @var HandlerStack Стек обработчиков запросов */
    private HandlerStack $stack;

    /**
     * Создает новый экземпляр HTTP клиента для работы с API Wildberries
     *
     * @param string $baseUrl Базовый URL API
     * @param string $apiKey API ключ для авторизации
     * @param string|null $proxyUrl URL прокси-сервера (например, ****************************************)
     */
    public function __construct(string $baseUrl, string $apiKey, ?string $proxyUrl)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;

        $this->stack = new HandlerStack();
        $this->stack->setHandler(new CurlHandler());

        $this->Client = new HttpClient([
            'timeout' => 0, // без ограничения времени ожидания
            'verify' => false, // отключаем проверку SSL-сертификатов
            'handler' => $this->stack,
            'proxy' => $proxyUrl,
        ]);


    }

    /**
     * Добавляет промежуточное ПО u043a стеку обработчиков HTTP-запросов
     *
     * @param callable $middleware Функция промежуточного ПО
     * @param string $name Название промежуточного ПО (опционально)
     */
    public function addMiddleware(callable $middleware, string $name = ''): void
    {
        $this->stack->push($middleware, $name);
    }

    /**
     * Выполняет HTTP-запрос к API Wildberries
     *
     * @param string $method HTTP-метод (GET, POST, PUT, PATCH, DELETE, MULTIPART)
     * @param string $path Путь к ресурсу API
     * @param array $params Параметры запроса (для GET - параметры строки запроса, для остальных - тело запроса)
     * @param array $addonHeaders Дополнительные заголовки запроса
     * @return mixed Ответ API (объект или строка)
     *
     * @throws RequestException Если произошла ошибка при выполнении запроса
     * @throws InvalidArgumentException Если передан неподдерживаемый HTTP-метод
     * @throws GuzzleException|JsonException Если произошла ошибка в Guzzle HTTP клиенте
     */
    public function request(string $method, string $path, array $params = [], array $addonHeaders = []): mixed
    {
        $this->responseCode = 0;
        $this->responsePhrase = null;
        $this->responseHeaders = [];
        $this->rawResponse = null;
        $this->response = null;

        $defaultHeaders = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => $this->apiKey,
        ];
        $headers = array_merge($defaultHeaders, $addonHeaders);
        $url = $this->baseUrl . $path;

        try {
            // Формируем запрос в зависимости от метода
            $response = match (strtoupper($method)) {
                'GET' => $this->Client->get($url, [
                    'headers' => $headers,
                    'query' => Query::build($params),
                ]),
                'POST' => $this->Client->post($url, [
                    'headers' => $headers,
                    'body' => json_encode(
                        $params,
                        JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
                    ),
                ]),
                'PUT' => $this->Client->put($url, [
                    'headers' => $headers,
                    'body' => json_encode(
                        $params,
                        JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
                    ),
                ]),
                'PATCH' => $this->Client->patch($url, [
                    'headers' => $headers,
                    'body' => json_encode(
                        $params,
                        JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
                    ),
                ]),
                'DELETE' => $this->Client->delete($url, [
                    'headers' => $headers,
                    'body' => json_encode(
                        $params,
                        JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
                    ),
                ]),
                'MULTIPART' => $this->Client->post($url, [
                    'headers' => array_merge([
                        'Authorization' => $this->apiKey,
                    ], $addonHeaders),
                    'multipart' => $params,
                ]),
                default => throw new InvalidArgumentException('Unsupported request method: ' . strtoupper($method)),
            };

        } catch (RequestException|ClientException $exc) {
            // Обработка ошибок запроса
            if ($exc->hasResponse()) {
                $response = $exc->getResponse();
                $this->responseCode = $response->getStatusCode();
                $this->responsePhrase = $response->getReasonPhrase();
                $this->responseHeaders = $response->getHeaders();

                // Попытка прочитать тело ответа
                $responseContent = $response->getBody()->getContents();
                $this->rawResponse = $responseContent;

                // Попытка распарсить JSON
                $jsonDecoded = json_decode($responseContent);
                if (json_last_error() === JSON_ERROR_NONE) {
                    /*
                      400 Bad Request
                      403 Forbidden
                      404 Not Found
                      409 Conflict
                      500 Internal Server Error
                          {["code"] => "InternalServerError", ["message"] => "Внутренняя ошибка сервера"}
                      ...
                     */
                    $this->response = $jsonDecoded;

                    return $jsonDecoded;
                }

                // Если не JSON, возвращаем сырой ответ
                $this->response = $responseContent;

                return $responseContent;
            }

            /*
              401 Unauthorized
              404 Not Found
              429 Too Many Requests
              0	cURL error 6: Could not resolve host
              0	cURL error 28: Operation timed out after * milliseconds with 0 out of 0 bytes received
              0	cURL error 56: OpenSSL SSL_read: Connection was reset, errno 10054
              0	cURL error 60: SSL certificate problem: self-signed certificate in certificate chain
              ...
             */
            // Если нет ответа, пробрасываем исключение дальше
            throw $exc;
        }

        // Заполняем информацию об ответе
        $this->responseCode = $response->getStatusCode();
        $this->responsePhrase = $response->getReasonPhrase();
        $this->responseHeaders = $response->getHeaders();

        // Заполняем информацию о лимитах запросов
        $this->rateLimit = (int)$response->getHeaderLine('X-RateLimit-Limit') ?: 0;
        $this->rateRemaining = (int)$response->getHeaderLine('X-RateLimit-Remaining') ?: 0;
        $this->rateReset = (int)$response->getHeaderLine('X-RateLimit-Reset') ?: 0;
        $this->rateRetry = (int)$response->getHeaderLine('X-RateLimit-Retry') ?: 0;

        // Получаем содержимое ответа
        $responseContent = $response->getBody()->getContents();
        $this->rawResponse = $responseContent;

        // Пытаемся распарсить JSON
        $jsonDecoded = json_decode($responseContent);

        // Если ответ не JSON, возвращаем сырой ответ
        $this->response = (json_last_error() === JSON_ERROR_NONE) ? $jsonDecoded : $responseContent;

        return $this->response;
    }
}
