<?php

declare(strict_types=1);

namespace App\Clients\Ozon\API;

use App\Clients\Ozon\Exception\ApiClientException;
use App\Clients\WB\Exception\ApiTimeRestrictionsException;
use GuzzleHttp\Exception\GuzzleException;
use InvalidArgumentException;
use JsonException;

/**
 * Базовый абстрактный класс для всех конечных точек API Ozon
 *
 * Предоставляет общую функциональность для выполнения HTTP-запросов,
 * обработки ошибок и управления ответами API.
 */
abstract class AbstractEndpoint
{
    /** @var Client HTTP клиент для выполнения запросов */
    private Client $Client;

    /**
     * Создает новый экземпляр конечной точки API
     *
     * @param string $baseUrl Базовый URL API
     * @param string $key API ключ для авторизации
     * @param string|null $proxy URL прокси-сервера (опционально)
     */
    public function __construct(string $baseUrl, string $key, int $clientId, ?string $proxy = null)
    {
        $this->Client = new Client(rtrim($baseUrl, '/'), $key, $clientId, $proxy);
        if (method_exists($this, 'middleware')) {
            $this->Client->addMiddleware($this->middleware());
        }
    }

    /**
     * Магический метод для перехвата вызовов методов запросов
     *
     * @param string $method Имя вызываемого метода
     * @param array $parameters Параметры метода
     * @return mixed Результат выполнения метода
     *
     * @throws InvalidArgumentException Если метод не найден или не является методом запроса
     */
    public function __call(string $method, array $parameters): mixed
    {
        if (method_exists($this, $method)
            && in_array($method, ['getRequest', 'postRequest', 'putRequest', 'patchRequest', 'deleteRequest', 'multipartRequest'])
        ) {
            return call_user_func_array([$this, $method], $parameters);
        }
        throw new InvalidArgumentException("Метод {$method} не существует или не является методом запроса");
    }

    /**
     * Проверка подключения к API
     *
     * @return object {TS: string, status: "OK"}
     */
    public function ping(): object
    {
        return $this->getRequest('/ping');
    }

    /**
     * Выполняет GET-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Параметры строки запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws GuzzleException
     */
    protected function getRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('GET', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет HTTP-запрос к API и обрабатывает ошибки
     *
     * @param string $method HTTP-метод (GET, POST, PUT, PATCH, DELETE, MULTIPART)
     * @param string $path Путь к ресурсу API
     * @param array $data Параметры запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws GuzzleException|JsonException Если действуют временные ограничения
     * @throws ApiClientException
     */
    private function request(string $method, string $path, array $data = [], array $addonHeaders = []): mixed
    {
        while (true) {
            $result = $this->Client->request($method, $path, $data, $addonHeaders);

            //Неверный параметр
            if ($this->responseCode() === 400) {
                throw new ApiClientException($result->message);
            }

            // Обработка ошибок авторизации
            if ($this->responseCode() === 403) {
                throw new ApiClientException($result->message);
            }

            // Обработка ошибок превышения лимита запросов
            if ($this->responseCode() === 409) {
                throw new ApiClientException($result->message);
            }

            // Обработка ошибок тайм-аута шлюза
            if ($this->responseCode() === 504 || $this->responseCode() === 500) {
                continue; // Продолжаем цикл и повторяем запрос
            }

            // Если нет ошибок, требующих повторного запроса, возвращаем результат
            return $result;
        }
    }

    /**
     * Возвращает HTTP код ответа последнего запроса
     *
     * @return int HTTP код ответа (200, 404, 500 и т.д.)
     */
    public function responseCode(): int
    {
        return $this->Client->responseCode;
    }

    /**
     * Возвращает текстовое описание HTTP кода ответа
     *
     * @return string|null Текстовое описание (OK, Not Found, Internal Server Error и т.д.)
     */
    public function responsePhrase(): ?string
    {
        return $this->Client->responsePhrase;
    }

    /**
     * Возвращает заголовки ответа
     *
     * @return array Массив заголовков ответа
     */
    public function responseHeaders(): array
    {
        return $this->Client->responseHeaders;
    }

    /**
     * Возвращает сырое содержимое ответа
     *
     * @return string|null Сырое содержимое ответа (обычно JSON-строка)
     */
    public function rawResponse(): ?string
    {
        return $this->Client->rawResponse;
    }

    /**
     * Возвращает обработанный ответ
     *
     * @return mixed Обработанный ответ (объект или строка)
     */
    public function response(): mixed
    {
        return $this->Client->response;
    }

    /**
     * Возвращает информацию о лимитах запросов API
     *
     * @return array Массив с информацией о лимитах запросов
     */
    public function responseRate(): array
    {
        return [
            'limit' => $this->Client->rateLimit,         // Максимальное количество запросов
            'remaining' => $this->Client->rateRemaining, // Оставшееся количество запросов
            'reset' => $this->Client->rateReset,         // Время сброса ограничения в секундах
            'retry' => $this->Client->rateRetry,         // Рекомендуемое время ожидания в миллисекундах
        ];
    }

    /**
     * Выполняет POST-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function postRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('POST', $path, $data, $addonHeaders);
    }

    /**
     * @throws GuzzleException
     * @throws ApiClientException
     * @throws JsonException
     */
    protected function postWithouBodyRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('POST_NO_BODY', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет PUT-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function putRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('PUT', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет PATCH-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function patchRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('PATCH', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет DELETE-запрос к API
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Тело запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function deleteRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('DELETE', $path, $data, $addonHeaders);
    }

    /**
     * Выполняет мультичастный запрос к API (для загрузки файлов)
     *
     * @param string $path Путь к ресурсу API
     * @param array $data Данные для мультичастного запроса
     * @param array $addonHeaders Дополнительные заголовки
     * @return mixed Ответ API
     *
     * @throws ApiTimeRestrictionsException Если действуют временные ограничения
     */
    protected function multipartRequest(string $path, array $data = [], array $addonHeaders = []): mixed
    {
        return $this->request('MULTIPART', $path, $data, $addonHeaders);
    }
}
