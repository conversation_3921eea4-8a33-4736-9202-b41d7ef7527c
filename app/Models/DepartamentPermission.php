<?php

namespace App\Models;

use App\Enums\Api\Internal\PermissionScopeEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class DepartamentPermission extends Model
{
    use HasFactory;

    protected $fillable = ['departament_id', 'permission_id', 'scope'];

    protected $casts = [
        'scope' => PermissionScopeEnum::class,
    ];

    public function departament(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'departament_id');
    }

    public function permission(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'permission_id');
    }
}
