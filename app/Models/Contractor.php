<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Contractor extends Model
{
    use HasFactory;
    use HasUuids;
    use SoftDeletes;

    protected $table = 'contractors';

    /**
    * The attributes that are mass assignable.
    *
    * @var array<int, string>
    */
    protected $fillable = [
        'id',                   // id
        'cabinet_id',
        'title',                // Наименование
        'status_id',            // Статус
        'buyer',                  // Телефон
        'supplier',                  // Телефон
        'tel',                  // Телефон
        'fax',                  // Факс
        'email',                // Элетронный адрес
        'actually_address',     // Фактический адрес
        'description_address',  // Комментарий к адресу
        'description',          // Комментарий
        'code',                 // Код
        'external_code',        // Внешний код
        'discounts_and_prices', // Скидки и цены  -Цена продажи
        'discount_card_number', // Номер диск. карты
        'employee',             // Подпись в отправляемых письмах
        'department_id',       // Отдел
        'shared_access',        // Общий доступ
    ];


    /**
     * @return BelongsToMany<Group>
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'contractor_group', 'contractor_id', 'group_id'); //
    }

    /**
    *  @return HasMany<Product>
    */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }


}
