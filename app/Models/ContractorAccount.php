<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractorAccount extends Model
{
    use HasUuids;
    use HasFactory;

    protected $fillable = [
        'contractor_id',
        'is_main',
        'bik',
        'correspondet_account',
        'payment_account',
        'balance',
        'bank',
        'address',
    ];
}
