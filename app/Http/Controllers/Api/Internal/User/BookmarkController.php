<?php

namespace App\Http\Controllers\Api\Internal\User;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\BookmarksPolicyContract;
use App\Contracts\Services\Internal\BookmarksServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Bookmarks\BookmarkIndexRequest;
use App\Http\Requests\Api\Internal\Bookmarks\BookmarkStoreRequest;
use App\Http\Requests\Api\Internal\Bookmarks\BookmarkUpdateRequest;
use App\Http\Resources\User\Bookmarks\BookmarkIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BookmarkController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly BookmarksServiceContract $service,
        private readonly BookmarksPolicyContract $policy
    ) {
    }

    /**
     * @response BookmarkIndexCollection<BookmarkIndexResource>
     */
    public function index(BookmarkIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new BookmarkIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(BookmarkStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(BookmarkUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
