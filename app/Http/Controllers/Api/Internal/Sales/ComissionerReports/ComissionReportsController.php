<?php

namespace App\Http\Controllers\Api\Internal\Sales\ComissionerReports;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\ComissionReportsPolicyContract;
use App\Contracts\Services\Internal\Sales\ComissionReportsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ComissionReports\ComissionReportsIndexRequest;
use App\Http\Requests\Api\Internal\Contract\ContractBulkRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;

class ComissionReportsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ComissionReportsServiceContract $service,
        private readonly ComissionReportsPolicyContract $policy
    ) {
    }

    public function index(ComissionReportsIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            return $this->successResponse($data);
        });
    }

    public function bulkCopy(ContractBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkCopy($data);

            //$this->service->bulkCopy($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkDelete(ContractBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            //$this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }
    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
