<?php

namespace App\Http\Requests\Api\Internal\Countries;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\References\CountriesService\DTO\CountryDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CountryUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'full_name' => 'nullable|string',
            'code' => 'nullable|string',
            'iso2' => 'nullable|string|size:2',
            'iso3' => 'nullable|string|size:3',
            'employee_id' => 'required|uuid',
            'department_id' => 'required|uuid',
            'is_common' => 'nullable|boolean',
        ];
    }

    public function toDTO(): CountryDTO
    {
        return CountryDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
