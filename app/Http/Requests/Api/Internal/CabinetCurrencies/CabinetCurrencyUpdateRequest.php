<?php

namespace App\Http\Requests\Api\Internal\CabinetCurrencies;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\CurrencyTypeEnum;
use App\Services\Api\Internal\References\CabinetCurrenciesService\DTO\CabinetCurrencyDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CabinetCurrencyUpdateRequest extends FormRequest implements ToDtoContract
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // Системная валюта обязательна для AUTO
            'currency_id' => [
                Rule::when(fn ($input) => $input['type'] == CurrencyTypeEnum::AUTO->value, ['required', 'UUID', 'exists:global_currencies,id']),
                Rule::when(fn ($input) => $input['type'] != CurrencyTypeEnum::AUTO->value, ['nullable', 'UUID', 'exists:global_currencies,id']),
            ],
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'is_common' => 'nullable|boolean',

            // Поля обязательны, только если currency_id не указан
            'num_code' => 'required_without:currency_id|string',
            'char_code' => 'required_without:currency_id|string|uppercase',
            'short_name' => 'required_without:currency_id|string',
            'name' => 'required_without:currency_id|string',

            'type' => ['required', Rule::in(CurrencyTypeEnum::cases())],
            'markup' => 'nullable|numeric',

            // Поля обязательны только если тип не AUTO
            'nominal' => [
                Rule::when(fn ($input) => $input['type'] != CurrencyTypeEnum::AUTO->value, ['required', 'numeric', 'min:1']),
                Rule::when(fn ($input) => $input['type'] == CurrencyTypeEnum::AUTO->value, ['nullable']),
            ],
            'value' => [
                Rule::when(fn ($input) => $input['type'] != CurrencyTypeEnum::AUTO->value, ['required', 'numeric', 'min:1']),
                Rule::when(fn ($input) => $input['type'] == CurrencyTypeEnum::AUTO->value, ['nullable']),
            ],
            'is_reverse' => 'nullable|boolean',

            // Плюрализация необязательна для AUTO
            'pluralization' => [
                Rule::when(fn ($input) => $input['type'] != CurrencyTypeEnum::AUTO->value, ['nullable', 'array']),
            ],
            'pluralization.*.gender' => 'in:m,f',
            'pluralization.*.single' => 'string',
            'pluralization.*.two' => 'string',
            'pluralization.*.five' => 'string',
            'is_other' => 'nullable|boolean',
        ];
    }

    public function toDTO(): CabinetCurrencyDTO
    {
        return CabinetCurrencyDTO::fromArray(array_merge(
            $this->validated(),
            ['resource_id' => $this->route('id')]
        ));
    }
}
