<?php

namespace App\Http\Requests\Api\Internal\Warehouses\Cells;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\WarehouseCellAddressSeparatorEnum;
use App\Enums\Api\Internal\WarehouseCellStocktakeTypeEnum;
use App\Enums\Api\Internal\WarehouseCellTypeEnum;
use App\Services\Api\Internal\Warehouses\WarehouseCellsService\DTO\WarehouseCellDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WarehouseCellStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        $excludeIfReceiving = 'exclude_if:type,' . WarehouseCellTypeEnum::RECEIVING->value;

        return [
            'group_id' => 'nullable|UUID',
            'warehouse_id' => 'required|UUID',
            'type' => 'required|string|' . Rule::in(WarehouseCellTypeEnum::cases()),

            // Адресация
            'address' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                $excludeIfReceiving,
                'nullable',
                'string',
                'max:255',
            ],
            'section' => [
                $excludeIfReceiving,
                'nullable',
                'string',
                'max:255',
            ],
            'line' => [
                $excludeIfReceiving,
                'nullable',
                'string',
                'max:255',
            ],
            'rack' => [
                $excludeIfReceiving,
                'nullable',
                'string',
                'max:255',
            ],
            'tier' => [
                $excludeIfReceiving,
                'nullable',
                'string',
                'max:255',
            ],
            'position' => [
                $excludeIfReceiving,
                'nullable',
                'string',
                'max:255',
            ],
            'separator' => [
                $excludeIfReceiving,
                'nullable',
                'string',
                Rule::in(WarehouseCellAddressSeparatorEnum::cases()),
                'max:255',
            ],

            // Размещение и отбор
            'availability_level' => [
                $excludeIfReceiving,
                'nullable',
                'integer',
            ],
            'circumvention_order' => [
                $excludeIfReceiving,
                'nullable',
                'integer',
            ],

            // Наполнение
            'size_id' => [
                $excludeIfReceiving,
                'nullable',
                'UUID',
            ],
            'filling_volume' => [
                $excludeIfReceiving,
                'nullable',
                'integer',
                'max:100',
            ],
            'filling_weight' => [
                $excludeIfReceiving,
                'nullable',
                'integer',
                'max:100',
            ],

            'stocktake' => [
                $excludeIfReceiving,
                'nullable',
                'integer',
                Rule::in(WarehouseCellStocktakeTypeEnum::cases())
            ],
            'recalculate_days' => [
                $excludeIfReceiving,
                'required_if:stocktake,' . WarehouseCellStocktakeTypeEnum::EVERY_N_DAYS->value,
                'nullable',
                'integer',
            ],

            //Зона хранения
            'storage_area_id' => [
                $excludeIfReceiving,
                'nullable',
                'UUID',
            ],
        ];
    }

    public function toDTO(): WarehouseCellDTO
    {
        return WarehouseCellDTO::fromArray($this->validated());
    }
}
