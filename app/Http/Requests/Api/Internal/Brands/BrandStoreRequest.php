<?php

namespace App\Http\Requests\Api\Internal\Brands;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Goods\Other\BrandsService\DTO\BrandDTO;
use Illuminate\Foundation\Http\FormRequest;

class BrandStoreRequest extends FormRequest implements ToDtoContract
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'name' => 'required|string|max:255',
        ];
    }

    public function toDTO(): BrandDTO
    {
        return BrandDTO::fromArray($this->validated());
    }
}
