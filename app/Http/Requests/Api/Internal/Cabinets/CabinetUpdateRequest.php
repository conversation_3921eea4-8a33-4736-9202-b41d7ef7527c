<?php

namespace App\Http\Requests\Api\Internal\Cabinets;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Workspace\CabinetsService\DTO\CabinetDTO;
use Illuminate\Foundation\Http\FormRequest;

class CabinetUpdateRequest extends FormRequest implements ToDtoContract
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
        ];
    }

    public function toDTO(): CabinetDTO
    {
        $data = $this->validated();
        $data['id'] = $this->route('id');

        return CabinetDTO::fromArray($data);
    }
}
