<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use function Aws\map;

class ContractorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'asd' => $this->when(!empty($this->contacts),
                map($this->groups, function ($item) {
                    return [
                        'id' => $item->id,
                        'full_name' => $item->full_name,
                        'position' => $item->position,
                        'phone' => $item->phone,
                        'email' => $item->email,
                        'comment' => $item->comment,
                        'created_at' => $item->created_at,
                        'updated_at' => $item->updated_at,
                    ];
                })
            )
        ];
    }
}
