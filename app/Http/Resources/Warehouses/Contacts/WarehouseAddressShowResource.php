<?php

namespace App\Http\Resources\Warehouses\Contacts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseAddressShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор адреса склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $country_id Идентификатор страны */
            'country_id' => $this->country_id,
            /** @var string|null $postcode Почтовый индекс */
            'postcode' => $this->postcode,
            /** @var string|null $region Регион */
            'region' => $this->region,
            /** @var string|null $city Город */
            'city' => $this->city,
            /** @var string|null $street Улица */
            'street' => $this->street,
            /** @var string|null $house Дом */
            'house' => $this->house,
            /** @var string|null $office Офис */
            'office' => $this->office,
            /** @var string|null $other Другое */
            'other' => $this->other,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
        ];
    }
}
