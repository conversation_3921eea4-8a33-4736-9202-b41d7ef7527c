<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Supplies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class SupplyIndexCollection extends ResourceCollection
{
    public $collects = SupplyIndexResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'page' => $this->additional['page'] ?? 1,
            'per_page' => $this->additional['per_page'] ?? 15,
        ];
    }
}
