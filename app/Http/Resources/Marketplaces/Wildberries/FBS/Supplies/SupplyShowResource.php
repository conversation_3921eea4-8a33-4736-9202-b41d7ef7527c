<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Supplies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupplyShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор поставки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->integration_id,
            /** @var string|null $supply_id ID поставки в Wildberries */
            'supply_id' => $this->supply_id,
            /** @var string $name Название поставки */
            'name' => $this->name,
            /** @var bool $done Флаг завершения поставки */
            'done' => (bool) $this->done,
            /** @var string|null $closed_at Дата закрытия поставки */
            'closed_at' => $this->closed_at,
            /** @var string|null $scanned_at Дата сканирования */
            'scanned_at' => $this->scanned_at,
            /** @var int|null $cargo_type Габаритный тип поставки */
            'cargo_type' => $this->cargo_type,
            /** @var string|null $created_at_wb Дата создания поставки в Wildberries */
            'created_at_wb' => $this->created_at_wb,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,

            /** @var array<int, array{id: string, created_at: string, updated_at: string, trbxIds: string, orders: array<int, array{id: string}>}> $boxes Коробки в поставке */
            'boxes' => collect($this->boxes ?? [])->map(function ($box) {
                return [
                    /** @var string $id Идентификатор коробки */
                    'id' => $box['id'],
                    /** @var string $created_at Дата создания коробки */
                    'created_at' => $box['created_at'],
                    /** @var string $updated_at Дата обновления коробки */
                    'updated_at' => $box['updated_at'],
                    /** @var string $trbxIds Идентификатор коробки в Wildberries */
                    'trbxIds' => $box['trbxIds'],
                    /** @var array<int, array{id: string}> $orders Заказы в коробке */
                    'orders' => collect($box['orders'] ?? [])->map(function ($order) {
                        return [
                            /** @var string $id Идентификатор заказа */
                            'id' => $order['id'],
                        ];
                    })->toArray(),
                ];
            })->toArray(),

            /** @var array<int, array{id: string}> $orders Заказы напрямую в поставке (не в коробках) */
            'orders' => collect($this->orders ?? [])->map(function ($order) {
                return [
                    /** @var string $id Идентификатор заказа */
                    'id' => $order['id'],
                ];
            })->toArray(),
        ];
    }
}
