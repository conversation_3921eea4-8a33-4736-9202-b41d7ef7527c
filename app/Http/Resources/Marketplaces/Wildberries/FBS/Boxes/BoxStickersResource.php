<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Boxes;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BoxStickersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var array<int, array{barcode: string, file: string}> $stickers Массив стикеров коробок */
            'stickers' => collect($this->resource['stickers'])->map(function ($sticker) {
                return [
                    /** @var string $barcode Штрихкод коробки */
                    'barcode' => $sticker->barcode,
                    /** @var string $file Base64 закодированный файл стикера */
                    'file' => $sticker->file,
                ];
            })->toArray(),
            /** @var string $filetype Тип файла стикеров */
            'filetype' => $this->resource['filetype'],
        ];
    }
}
