<?php

namespace App\Http\Resources\References\SalesChannels;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalesChannelShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор канала продаж */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $name Название канала продаж */
            'name' => (string) $this->name,
            /** @var string $sales_channel_type_id Идентификатор типа канала продаж */
            'sales_channel_type_id' => $this->sales_channel_type_id,
            /** @var string|null $description Описание */
            'description' => $this->description,
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_default По умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var bool $is_common Общий */
            'is_common' => (bool) $this->is_common,
            /** @var int $sort Порядок сортировки */
            'sort' => (int) $this->sort,
        ];
    }
}
