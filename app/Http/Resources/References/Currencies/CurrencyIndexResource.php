<?php

namespace App\Http\Resources\References\Currencies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CurrencyIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор валюты */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function() {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string|null $currency_id Идентификатор глобальной валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function() {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var bool $is_accouting Учетная валюта */
            'is_accouting' => $this->when(
                property_exists($this->resource, 'is_accouting'),
                function() {
                    return (bool) $this->resource->is_accouting;
                }
            ),
            /** @var string|null $external_id Внешний идентификатор */
            'external_id' => $this->when(
                property_exists($this->resource, 'external_id'),
                function() {
                    return $this->resource->external_id;
                }
            ),
            /** @var string|null $num_code Числовой код */
            'num_code' => $this->when(
                property_exists($this->resource, 'num_code'),
                function() {
                    return $this->resource->num_code;
                }
            ),
            /** @var string|null $char_code Символьный код */
            'char_code' => $this->when(
                property_exists($this->resource, 'char_code'),
                function() {
                    return $this->resource->char_code;
                }
            ),
            /** @var string|null $short_name Краткое название */
            'short_name' => $this->when(
                property_exists($this->resource, 'short_name'),
                function() {
                    return $this->resource->short_name;
                }
            ),
            /** @var string|null $name Название */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return $this->resource->name;
                }
            ),
            /** @var int $type Тип валюты */
            'type' => $this->when(
                property_exists($this->resource, 'type'),
                function() {
                    return (int) $this->resource->type;
                }
            ),
            /** @var string $markup Наценка */
            'markup' => $this->when(
                property_exists($this->resource, 'markup'),
                function() {
                    return (string) $this->resource->markup;
                }
            ),
            /** @var string $nominal Номинал */
            'nominal' => $this->when(
                property_exists($this->resource, 'nominal'),
                function() {
                    return (string) $this->resource->nominal;
                }
            ),
            /** @var string $value Курс */
            'value' => $this->when(
                property_exists($this->resource, 'value'),
                function() {
                    return (string) $this->resource->value;
                }
            ),
            /** @var bool $is_reverse Обратный курс */
            'is_reverse' => $this->when(
                property_exists($this->resource, 'is_reverse'),
                function() {
                    return (bool) $this->resource->is_reverse;
                }
            ),
            /** @var array|null $pluralization Склонения */
            'pluralization' => $this->when(
                property_exists($this->resource, 'pluralization'),
                function() {
                    return $this->resource->pluralization && is_string($this->resource->pluralization) ? json_decode($this->resource->pluralization, true) : ($this->resource->pluralization ?? null);
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var bool $is_common Общая валюта */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function() {
                    return (bool) $this->resource->is_common;
                }
            ),
            /** @var bool $is_other Другая валюта */
            'is_other' => $this->when(
                property_exists($this->resource, 'is_other'),
                function() {
                    return (bool) $this->resource->is_other;
                }
            ),
        ];
    }
}
