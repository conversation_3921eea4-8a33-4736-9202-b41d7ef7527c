<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseCellPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\Warehouses\WarehouseCellsService\DTO\WarehouseCellDTO;

readonly class WarehouseCellPolicy implements WarehouseCellPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseCellDTO) {
            return;
        }

        $warehouse = $this->authService->validateRelationAccess(
            'warehouses',
            $dto->warehouse_id,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'update',
        );

        if ($dto->group_id) {
            $this->authService->validateRelationAccess(
                entity: 'warehouse_cell_groups',
                entityId: $dto->group_id,
                relatedEntity: [
                    'table' => 'warehouses',
                    'field' => 'warehouse_id',
                    'value' => $dto->group_id
                ]
            );
        }

        if ($dto->size_id) {
            $this->authService->validateRelationAccess(
                'warehouse_cell_sizes',
                $dto->size_id,
                $warehouse->cabinet_id,
            );
        }
        if ($dto->storage_area_id) {
            $this->authService->validateRelationAccess(
                entity: 'warehouse_storage_areas',
                entityId: $dto->storage_area_id,
                relatedEntity: [
                    'table' => 'warehouses',
                    'field' => 'warehouse_id',
                    'value' => $dto->storage_area_id
                ]
            );
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseCellDTO) {
            return;
        }

        $cell = $this->authService->validateRelationAccess(
            'warehouse_cells',
            $dto->resourceId,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'update',
            null,
            [
                'table' => 'warehouses',
                'field' => 'warehouse_id',
                'value' => $dto->resourceId
            ]
        );

        if ($dto->group_id && $dto->group_id != $cell->group_id) {
            $this->authService->validateRelationAccess(
                entity: 'warehouse_cell_groups',
                entityId: $dto->group_id,
                relatedEntity: [
                    'table' => 'warehouses',
                    'field' => 'warehouse_id',
                    'value' => $dto->group_id
                ]
            );
        }

        if ($dto->size_id && $dto->size_id != $cell->size_id) {
            $this->authService->validateRelationAccess(
                'warehouse_cell_sizes',
                $dto->size_id,
                $cell->cabinet_id,
            );
        }

    }

    public function index(string $warehouseId): void
    {
        $this->authService->validateRelationAccess(
            'warehouses',
            $warehouseId,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'warehouse_cells',
            $resourceId,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'view',
            null,
            [
                'table' => 'warehouses',
                'field' => 'warehouse_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'warehouse_cells',
            $resourceId,
            null,
            PermissionNameEnum::WAREHOUSES->value,
            'update',
            null,
            [
                'table' => 'warehouses',
                'field' => 'warehouse_id',
                'value' => $resourceId
            ]
        );
    }
}
