<?php

namespace App\Console\Commands;

use App\Traits\HasOrderedUuid;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillStatuses extends Command
{
    use HasOrderedUuid;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fill-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $this->info('Заполняем таблицу "status_types"...');

            $defaultData = require base_path('app/Data/Statuses.php');

            $typeIds = [];
            foreach ($defaultData['types'] as $type) {
                $typeIds[$type] = $this->generateUuid();

                DB::table('status_types')->insert(
                    [
                        'id' => $typeIds[$type],
                        'name' => $type
                    ]
                );
            }
            $this->info('Успешно!');

            $this->info('Заполняем таблицу "statuses"...');

            foreach ($defaultData['statuses'] as $key => $value) {
                foreach ($value as $item) {
                    DB::table('statuses')->insert(
                        [
                            'id' => $this->generateUuid(),
                            'name' => $item['name'],
                            'color' => $item['color'],
                            'type_id' => $typeIds[$key]
                        ]
                    );
                }
            }
            $this->info('Успешно!');

            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Ошибка при выполнении команды: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
