<?php

namespace App\Console\Commands;

use App\Entities\AcceptanceItemEntity;
use App\Extensions\Scramble\FormRequestExtension;
use Dedoc\Scramble\Infer;
use Dedoc\Scramble\Support\Generator\TypeTransformer;
use Dedoc\Scramble\GeneratorConfig;
use Illuminate\Console\Command;
use ReflectionClass;

class TestFieldsCommand extends Command
{
    protected $signature = 'test:fields';
    protected $description = 'Test fields generation for AcceptanceItemEntity';

    public function handle()
    {
        try {
            // Create entity instance
            $entity = new AcceptanceItemEntity();

            // Test the getAllowedFields method directly
            $allowedFields = $entity->getAllowedFields();
            
            $this->info("All allowed fields:");
            foreach (array_slice($allowedFields, 0, 50) as $field) {
                $this->line("- $field");
            }
            
            $this->info("\nTotal fields: " . count($allowedFields));
            
            // Test structured fields
            $extension = new FormRequestExtension(
                app(Infer::class),
                app(TypeTransformer::class),
                app(GeneratorConfig::class)
            );
            
            $reflection = new ReflectionClass(FormRequestExtension::class);
            $method = $reflection->getMethod('getStructuredAllowedFields');
            $method->setAccessible(true);
            
            $structuredFields = $method->invoke($extension, $entity);
            
            $this->info("\nStructured fields:");
            foreach ($structuredFields as $field) {
                $this->line("- $field");
            }
            
            $this->info("\nTotal structured fields: " . count($structuredFields));
            
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->error("File: " . $e->getFile() . ":" . $e->getLine());
        }
    }
}
