<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Enums\Api\Internal\SuggestTypeEnum;

class DeleteOldSuggestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-old-suggestions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        DB::table('suggestions')
            ->where(function ($query) {
                $query->where('type', SuggestTypeEnum::BANK)
                    ->orWhere('type', SuggestTypeEnum::PARTY);
            })
            ->where('created_at', '<', Carbon::now()->subDays(30))
            ->delete();
    }
}
