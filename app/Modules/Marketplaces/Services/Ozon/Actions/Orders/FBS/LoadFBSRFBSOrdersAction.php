<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS;

use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base\BaseLoadOrdersAction;
use App\Modules\Marketplaces\Services\Ozon\Jobs\Orders\SyncFBSRFBSOrdersJob;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Queue;
use Throwable;

/**
 * Action для загрузки FBS и RFBS заказов
 */
class LoadFBSRFBSOrdersAction extends BaseLoadOrdersAction
{
    /**
     * @param string $integrationId ID интеграции
     * @param string|null $dateFrom Дата начала
     * @param string|null $dateTo Дата окончания
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     * @throws Throwable
     */
    public function run(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        $integration = $this->getIntegrationData($integrationId);

        $parameters = $this->prepareJobParameters($integration, $dateFrom, $dateTo);

        Queue::push(new SyncFBSRFBSOrdersJob(
            cabinetId: $parameters['cabinet_id'],
            apiKey: $parameters['api_key'],
            clientId: $parameters['client_id'],
            integrationId: $parameters['integration_id'],
            legalEntityId: $parameters['legal_entity_id'],
            employeeId: $parameters['employee_id'],
            departmentId: $parameters['department_id'],
            contractorId: $parameters['contractor_id'],
            orderNumberingType: $parameters['order_numbering_type'],
            addPrefix: $parameters['add_prefix'],
            prefix: $parameters['prefix'],
            reserve: $parameters['reserve'],
            dateFrom: $parameters['date_from'],
            dateTo: $parameters['date_to']
        ));
    }
}
