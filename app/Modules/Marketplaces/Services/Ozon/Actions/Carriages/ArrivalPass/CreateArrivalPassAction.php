<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Carriages\ArrivalPass;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class CreateArrivalPassAction
{
    public function run(string $carriageId, array $arrivalPasses): array
    {
        $carriage = $this->getCarriageWithIntegration($carriageId);

        try {
            DB::beginTransaction();

            $ozonResponse = $this->createArrivalPassInOzon($carriage, $arrivalPasses);

            $savedPasses = $this->saveArrivalPasses($carriageId, $arrivalPasses, $ozonResponse->arrival_pass_ids);

            $this->updateCarriageDetails($carriageId, $ozonResponse->arrival_pass_ids);

            DB::commit();

            return [
                'carriage_id' => $carriageId,
                'ozon_carriage_id' => $carriage->ozon_carriage_id,
                'arrival_pass_ids' => $ozonResponse->arrival_pass_ids,
                'passes_count' => count($ozonResponse->arrival_pass_ids),
                'passes' => $savedPasses,
            ];

        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    protected function getCarriageWithIntegration(string $carriageId): object
    {
        $carriage = DB::table('ozon_carriages as c')
            ->join('ozon_integrations as i', 'c.integration_id', '=', 'i.id')
            ->where('c.id', $carriageId)
            ->select([
                'c.id',
                'c.ozon_carriage_id',
                'c.status',
                'i.api_key',
                'i.client_id'
            ])
            ->first();

        if (!$carriage) {
            throw new NotFoundException('Carriage not found');
        }

        return $carriage;
    }

    protected function createArrivalPassInOzon(object $carriage, array $arrivalPasses): object
    {
        $api = new API(
            apiKey: decrypt($carriage->api_key),
            clientId: decrypt($carriage->client_id)
        );

        return $api->FBS()->createArrivalPass(
            carriageId: (int) $carriage->ozon_carriage_id,
            arrivalPasses: $arrivalPasses
        );
    }

    protected function saveArrivalPasses(string $carriageId, array $arrivalPasses, array $ozonPassIds): array
    {
        $passRecords = [];
        $now = now();

        foreach ($arrivalPasses as $index => $passData) {
            $passRecords[] = [
                'id' => \Illuminate\Support\Str::orderedUuid()->toString(),
                'carriage_id' => $carriageId,
                'ozon_pass_id' => $ozonPassIds[$index] ?? null,
                'driver_name' => $passData['driver_name'],
                'driver_phone' => $passData['driver_phone'],
                'vehicle_license_plate' => $passData['vehicle_license_plate'],
                'vehicle_model' => $passData['vehicle_model'],
                'with_returns' => $passData['with_returns'] ?? false,
                'status' => 'created',
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::table('ozon_arrival_passes')->insert($passRecords);

        return $passRecords;
    }

    protected function updateCarriageDetails(string $carriageId, array $arrivalPassIds): void
    {
        $existingDetails = DB::table('ozon_carriage_details')
            ->where('carriage_id', $carriageId)
            ->first();

        if ($existingDetails) {
            DB::table('ozon_carriage_details')
                ->where('carriage_id', $carriageId)
                ->update([
                    'arrival_pass_ids' => json_encode($arrivalPassIds, JSON_THROW_ON_ERROR),
                    'updated_at' => now(),
                ]);
        } else {
            DB::table('ozon_carriage_details')->insert([
                'id' => $this->generateUuid(),
                'carriage_id' => $carriageId,
                'arrival_pass_ids' => json_encode($arrivalPassIds, JSON_THROW_ON_ERROR),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
