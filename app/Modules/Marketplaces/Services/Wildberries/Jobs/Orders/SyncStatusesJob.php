<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderStatusEnum;
use App\Services\Api\RateLimiter\Facades\RateLimiter;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Job для синхронизации статусов заказов из Wildberries
 */
class SyncStatusesJob implements ShouldQueue
{
    use HasOrderedUuid;
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    private API $api;

    /**
     * @throws WBSellerException
     */
    public function __construct(
        private readonly string $integrationId,
        private readonly string $token
    ) {
        $this->api = new API(['masterkey' => $this->token]);
    }

    /**
     * @throws BindingResolutionException
     * @throws Exception|Throwable
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();

            $statusTypeId = DB::table('status_types')
                ->where('name', 'customer_orders')
                ->value('id');

            $statuses = [];

            // Получаем заказы из всех типов таблиц
            $orders = $this->getAllOrders();

            if ($orders->isEmpty()) {
                DB::commit();
                return;
            }

            $orderChunks = $orders->chunk(1000);

            foreach ($orderChunks as $orderChunk) {

                $orderNumbers = $orderChunk->pluck('wb_number')->toArray();

                try {
                    RateLimiter::throttle('marketplace');
                    $wbStatuses = $this->api->Marketplace()->getOrdersStatuses($orderNumbers);
                } catch (Exception $e) {
                    Log::error('WB API error: ' . $e->getMessage(), [
                        'integration_id' => $this->integrationId,
                    ]);
                    continue;
                }

                $wbStatusesMap = collect($wbStatuses)->keyBy('id');

                foreach ($orderChunk as $order) {

                    if (!isset($wbStatusesMap[$order->wb_number])) {
                        continue;
                    }

                    $wbStatus = $wbStatusesMap[$order->wb_number];

                    if ($wbStatus['wbStatus'] == $order->wb_status) {
                        continue;
                    }

                    // Обновляем статус в соответствующей таблице
                    $this->updateOrderStatus($order, $wbStatus['wbStatus']);

                    if (!$order->customer_order_id) {
                        continue;
                    }

                    $statusKey = OrderStatusEnum::from($wbStatus['wbStatus'])->value;

                    if (!isset($statuses[$statusKey])) {
                        $status = DB::table('statuses')
                            ->where('type_id', $statusTypeId)
                            ->where('cabinet_id', $order->cabinet_id)
                            ->where('name', $statusKey)
                            ->first();

                        if (!$status) {
                            $statusId = $this->generateUuid();
                            DB::table('statuses')
                                ->insert([
                                    'id' => $statusId,
                                    'name' => $statusKey,
                                    'type_id' => $statusTypeId,
                                    'color' => '#9f9f9f',
                                    'cabinet_id' => $order->cabinet_id
                                ]);
                            $statuses[$statusKey] = $statusId;
                        } else {
                            $statuses[$statusKey] = $status->id;
                        }
                    }

                    DB::table('customer_orders')
                        ->where('id', $order->customer_order_id)
                        ->update([
                            'status_id' => $statuses[$statusKey]
                        ]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('SyncStatusesJob failed: ' . $e->getMessage(), [
                'integration_id' => $this->integrationId,
                'exception' => $e,
            ]);
            throw $e;
        }
    }

    /**
     * Получение заказов из всех типов таблиц
     */
    private function getAllOrders(): Collection
    {
        $orders = collect();

        // FBS заказы
        $fbsOrders = DB::table('wildberries_fbs_orders')
            ->where('integration_id', $this->integrationId)
            ->select(['id', 'wb_number', 'wb_status', 'customer_order_id', 'cabinet_id'])
            ->addSelect(DB::raw("'fbs' as order_type"))
            ->get();

        $orders = $orders->merge($fbsOrders);

        // DBS заказы
        $dbsOrders = DB::table('wildberries_dbs_orders')
            ->where('integration_id', $this->integrationId)
            ->select(['id', 'wb_number', 'wb_status', 'customer_order_id', 'cabinet_id'])
            ->addSelect(DB::raw("'dbs' as order_type"))
            ->get();

        $orders = $orders->merge($dbsOrders);

        // Self Delivery заказы
        $selfDeliveryOrders = DB::table('wildberries_self_delivery_orders')
            ->where('integration_id', $this->integrationId)
            ->select(['id', 'wb_number', 'wb_status', 'customer_order_id', 'cabinet_id'])
            ->addSelect(DB::raw("'self_delivery' as order_type"))
            ->get();

        return $orders->merge($selfDeliveryOrders);
    }

    /**
     * Обновление статуса заказа в соответствующей таблице
     * @throws Exception
     */
    private function updateOrderStatus(object $order, string $newStatus): void
    {
        $tableName = match ($order->order_type) {
            'fbs' => 'wildberries_fbs_orders',
            'dbs' => 'wildberries_dbs_orders',
            'self_delivery' => 'wildberries_self_delivery_orders',
            default => throw new Exception("Unknown order type: {$order->order_type}")
        };

        DB::table($tableName)
            ->where('id', $order->id)
            ->update([
                'wb_status' => $newStatus,
                'updated_at' => Carbon::now()
            ]);
    }
}
