<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders;

use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

readonly class GetUnmatchedItemsAction
{
    /**
     * Получение списка несопоставленных товаров в заказе
     *
     * @param string $orderId ID заказа
     * @return Collection Коллекция несопоставленных товаров
     */
    public function run(string $orderId): Collection
    {
        try {
            // Ищем заказ во всех типах таблиц
            $order = $this->findOrder($orderId);

            if (!$order) {
                return collect();
            }

            // Получаем несопоставленные товары заказа
            $items = $this->getUnmatchedItems($orderId, $order->order_type);

            if ($items->isEmpty()) {
                return collect();
            }

            // Для каждого товара получаем список товаров для сопоставления
            $result = collect();

            foreach ($items as $item) {
                // Добавляем информацию о товаре
                $itemData = (object)[
                    'id' => $item->id,
                    'order_id' => $item->order_id, // Изменено с wildberries_order_id
                    'nm_id' => $item->nm_id,
                    'chrt_id' => $item->chrt_id,
                    'article' => $item->article,
                    'sku' => $item->sku,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'is_matched' => $item->is_matched,
                ];

                // Получаем список товаров для сопоставления
                $productsToMatch = DB::table('wildberries_products_to_match')
                    ->where('cabinet_id', $order->cabinet_id)
                    ->where('wildberries_integration_id', $order->integration_id)
                    ->where('wb_id', $item->nm_id)
                    ->where(function ($query) use ($item) {
                        $query->where('size_id', $item->chrt_id)
                            ->orWhereNull('size_id');
                    })
                    ->where('is_matched', false)
                    ->get();

                foreach ($productsToMatch as $productToMatch) {
                    $productToMatch->skus = json_decode($productToMatch->skus);
                }

                $itemData->products_to_match = $productsToMatch;

                // Получаем список товаров в системе для сопоставления
                $products = DB::table('products')
                    ->where('cabinet_id', $order->cabinet_id)
                    ->where(function ($query) use ($item) {
                        if ($item->article) {
                            $query->where('article', 'like', "%{$item->article}%");
                        }
                        if ($item->sku) {
                            $query->orWhereExists(function ($subquery) use ($item) {
                                $subquery->select(DB::raw(1))
                                    ->from('barcodes')
                                    ->whereRaw('barcodes.barcodable_id = products.id')
                                    ->where('barcodes.barcodable_type', 'products')
                                    ->where('barcodes.value', $item->sku);
                            });
                        }
                    })
                    ->select('id', 'title', 'article', 'code')
                    ->limit(10)
                    ->get();

                $itemData->suggested_products = $products;

                $result->push($itemData);
            }

            return $result;
        } catch (Exception $e) {
            Log::error('Error getting unmatched items for Wildberries order', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $orderId,
            ]);

            return collect();
        }
    }

    /**
     * Поиск заказа во всех типах таблиц
     */
    private function findOrder(string $orderId): ?object
    {
        // FBS заказы
        $order = DB::table('wildberries_fbs_orders')
            ->where('id', $orderId)
            ->select(['*', DB::raw("'fbs' as order_type")])
            ->first();

        if ($order) {
            return $order;
        }

        // DBS заказы
        $order = DB::table('wildberries_dbs_orders')
            ->where('id', $orderId)
            ->select(['*', DB::raw("'dbs' as order_type")])
            ->first();

        if ($order) {
            return $order;
        }

        // Self Delivery заказы
        $order = DB::table('wildberries_self_delivery_orders')
            ->where('id', $orderId)
            ->select(['*', DB::raw("'self_delivery' as order_type")])
            ->first();

        return $order;
    }

    /**
     * Получение несопоставленных товаров заказа
     */
    private function getUnmatchedItems(string $orderId, string $orderType): Collection
    {
        $tableName = match ($orderType) {
            'fbs' => 'wildberries_fbs_order_items',
            'dbs' => 'wildberries_dbs_order_items',
            'self_delivery' => 'wildberries_self_delivery_order_items',
            default => throw new Exception("Unknown order type: {$orderType}")
        };

        return DB::table($tableName)
            ->where('order_id', $orderId)
            ->where('is_matched', false)
            ->get();
    }
}
