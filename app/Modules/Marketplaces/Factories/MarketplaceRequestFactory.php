<?php

namespace App\Modules\Marketplaces\Factories;

use App\Modules\Marketplaces\Contracts\MarketplaceRequestFactoryInterface;
use App\Modules\Marketplaces\Http\Requests\BaseUpdateCostAccoutingSettingsRequest;
use App\Modules\Marketplaces\Http\Requests\OzonStoreRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\UpdateCostAccoutingSettingsRequest;
use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use InvalidArgumentException;

class MarketplaceRequestFactory implements MarketplaceRequestFactoryInterface
{
    /*public static function createOrUpdateRequest(string $type, Request $request)
    {
        $requestClass = match ($type) {
            'ozon' => OzonStoreRequest::class,
            'wildberries' => WildberriesStoreMarketplaceRequest::class,
            default => throw new InvalidArgumentException("Unsupported marketplace type: {$type}")
        };

        return self::prepareRequest($requestClass, $request);
    }*/

    public static function updateCostAccountingSettings(string $type, Request $request): BaseUpdateCostAccoutingSettingsRequest
    {
        $requestClass = match ($type) {
            'ozon' => OzonStoreRequest::class,
            'wildberries' => UpdateCostAccoutingSettingsRequest::class,
            default => throw new InvalidArgumentException("Unsupported marketplace type: {$type}")
        };

        return self::prepareRequest($requestClass, $request);
    }

    /**
     * @param string $requestClass
     * @param Request $request
     * @return Application|mixed|string
     */
    private static function prepareRequest(string $requestClass, Request $request): mixed
    {
        $marketplaceRequest = app($requestClass);

        $validatedData = $request->validate(
            $marketplaceRequest->rules(),
            $marketplaceRequest->messages(),
            $marketplaceRequest->attributes()
        );

        $marketplaceRequest->merge($validatedData);

        return $marketplaceRequest;
    }
}
