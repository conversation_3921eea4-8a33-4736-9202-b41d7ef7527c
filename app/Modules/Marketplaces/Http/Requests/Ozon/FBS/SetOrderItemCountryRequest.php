<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\FBS;

use Illuminate\Foundation\Http\FormRequest;

class SetOrderItemCountryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'sku' => ['required', 'integer'],
            'country_iso2' => ['exists:countries,iso2', 'required', 'string', 'uppercase']
        ];
    }
}
