<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\FBS;

use Illuminate\Foundation\Http\FormRequest;

class GetPackageLabelsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_ids' => ['required', 'array', 'min:1', 'max:20'],
            'order_ids.*' => ['required', 'uuid'],
        ];
    }
}
