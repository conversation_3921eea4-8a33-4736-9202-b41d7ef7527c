<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\FBS;

use Illuminate\Foundation\Http\FormRequest;

class CancelFBSOrderItemsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cancel_reason_id' => ['required', 'integer'],
            'cancel_reason_message' => ['required', 'string'],
            'items.*' => ['required', 'array'],
            'items.*.id' => ['required', 'uuid'],
            'items.*.quantity' => ['required', 'integer', 'min:1'],
        ];
    }
}
