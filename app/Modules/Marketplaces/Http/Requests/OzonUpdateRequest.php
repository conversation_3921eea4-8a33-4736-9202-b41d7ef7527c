<?php

namespace App\Modules\Marketplaces\Http\Requests;

use App\Contracts\Requests\ToDtoContract;
use App\Modules\Marketplaces\Services\Ozon\Data\OzonMarketData;
use App\Modules\Marketplaces\Services\Ozon\Enums\OrderNumberingTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class OzonUpdateRequest extends FormRequest implements ToDtoContract
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|string|uuid',
            'name' => 'required|string|max:255',
            'client_id' => 'required|string|max:100',
            'api_key' => 'required|string',
            'legal_entity_id' => 'required|string|uuid',
            'contractor_id' => 'required|string|uuid',
            'comission_contract_id' => 'required|uuid',
            'department_id' => 'required|string|uuid',
            'price_sync' => 'array',
            'order_sync' => 'array',
            'report_sync' => 'array',

            'price_sync.your_price_id' => 'required|string|uuid',
            'price_sync.prediscount_price_id' => 'sometimes|nullable|string|uuid',
            'price_sync.min_price_id' => 'sometimes|nullable|string|uuid',
            'price_sync.auto_sync' => 'sometimes|boolean',

            'order_sync.num_type' => ['sometimes', 'string', Rule::in(OrderNumberingTypeEnum::cases())],
            'order_sync.add_prefix' => 'sometimes|boolean',
            'order_sync.prefix' => 'sometimes|string|max:50',
            'order_sync.use_common_block_contract' => 'sometimes|boolean',
            'order_sync.reserve' => 'sometimes|boolean',
            'order_sync.sync_order_statuses' => 'sometimes|boolean',
            'order_sync.auto_accept_orders' => 'sometimes|boolean',
            'order_sync.fair_mark' => 'sometimes|boolean',
            'order_sync.auto_sync' => 'sometimes|boolean',

            'report_sync.auto_sync' => 'sometimes|boolean',
        ];
    }

    public function toDto(): OzonMarketData
    {
        return OzonMarketData::fromArray(
            $this->validated()
        );
    }
}
