<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Carriages;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSCarriagesIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $syncInfo = $this->sync_info && is_string($this->sync_info) ? json_decode($this->sync_info, false, 512, JSON_THROW_ON_ERROR) : ($this->sync_info ?? []);
        $ozonInfo = $this->ozon_info && is_string($this->ozon_info) ? json_decode($this->ozon_info, false, 512, JSON_THROW_ON_ERROR) : ($this->ozon_info ?? []);

        $deliveryMethod = $this->delivery_method && is_string($this->delivery_method) ? json_decode($this->delivery_method, false, 512, JSON_THROW_ON_ERROR) : ($this->delivery_method ?? []);
        $warehouse = $this->warehouse && is_string($this->warehouse) ? json_decode($this->warehouse, false, 512, JSON_THROW_ON_ERROR) : ($this->warehouse ?? []);

        return [
            /** @var int $id ID отгрузки */
            'id' => $this->id,
            /** @var string $ozon_carriage_id ID отгрузки в системе Ozon */
            'ozon_carriage_id' => $this->ozon_carriage_id,
            /** @var string $departure_date Дата отправки */
            'departure_date' => $this->departure_date,
            /** @var string $status Статус отгрузки */
            'status' => $this->status,
            /** @var string|null $ozon_status Статус отгрузки в системе Ozon */
            'ozon_status' => $this->ozon_status,
            /** @var object{is_synced: bool, sync_attempts: int, last_sync_attempt: string|null} $sync_info Информация о синхронизации */
            'sync_info' => !empty($syncInfo) ? [
                /** @var bool $is_synced Отгрузка синхронизирована */
                'is_synced' => $syncInfo->is_synced,
                /** @var int $sync_attempts Количество попыток синхронизации */
                'sync_attempts' => $syncInfo->sync_attempts,
                /** @var string|null $last_sync_attempt Время последней попытки синхронизации */
                'last_sync_attempt' => $syncInfo->last_sync_attempt,
            ] : [],
            /** @var object{is_partial: bool, partial_num: int|null, containers_count: int}|null $ozon_info Информация от Ozon (только для синхронизированных) */
            'ozon_info' => !empty($ozonInfo) ? [
                /** @var bool $is_partial Отгрузка частичная */
                'is_partial' => $ozonInfo->is_partial,
                /** @var int|null $partial_num Номер частичной отгрузки */
                'partial_num' => $ozonInfo->partial_num,
                /** @var int $containers_count Количество контейнеров */
                'containers_count' => $ozonInfo->containers_count,
            ] : [],
            /** @var object{id: int, name: string, ozon_delivery_method_id: string} $delivery_method Информация о способе доставки */
            'delivery_method' => !empty($deliveryMethod) ? [
                /** @var string $id ID способа доставки */
                'id' => $deliveryMethod->id,
                /** @var string $name Название способа доставки */
                'name' => $deliveryMethod->name,
                /** @var string $ozon_delivery_method_id ID способа доставки в системе Ozon */
                'ozon_delivery_method_id' => $deliveryMethod->ozon_delivery_method_id,
            ] : [],
            /** @var object{name: string, ozon_warehouse_id: string} $warehouse Информация о складе */
            'warehouse' => !empty($warehouse) ? [
                /** @var string $name Название склада */
                'name' => $warehouse->name,
                /** @var string $ozon_warehouse_id ID склада в системе Ozon */
                'ozon_warehouse_id' => $warehouse->ozon_warehouse_id,
            ] : [],
            /** @var int $postings_count Количество отправлений в отгрузке */
            'postings_count' => $this->postings_count,
            /** @var string $created_at Дата создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата последнего обновления */
            'updated_at' => $this->updated_at,
        ];
    }
}
