<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSOrderShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор заказа */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания заказа */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->integration_id,
            /** @var string $ozon_order_id Идентификатор заказа в системе Ozon */
            'ozon_order_id' => $this->ozon_order_id,
            /** @var string $posting_number Номер отправления */
            'posting_number' => $this->posting_number,
            /** @var string $status Статус заказа */
            'status' => $this->status,
            /** @var string|null $status_description Описание статуса */
            'status_description' => $this->status_description,
            /** @var string $order_date Дата создания заказа в системе Ozon */
            'order_date' => $this->order_date,
            /** @var string|null $shipment_date Дата отгрузки */
            'shipment_date' => $this->shipment_date,
            /** @var string|null $delivery_date Дата доставки */
            'delivery_date' => $this->delivery_date,
            /** @var string|null $cutoff_date Дата отсечки */
            'cutoff_date' => $this->cutoff_date,
            /** @var bool $is_express Флаг экспресс-доставки */
            'is_express' => (bool) $this->is_express,
            /** @var bool $is_multibox Флаг мультибокс отправления */
            'is_multibox' => (bool) $this->is_multibox,
            /** @var string $total_price Общая стоимость заказа */
            'total_price' => (string) $this->total_price,
            /** @var string $commission_amount Сумма комиссии */
            'commission_amount' => (string) $this->commission_amount,
            /** @var string|null $tracking_number Трек-номер отправления */
            'tracking_number' => $this->tracking_number,
            /** @var string|null $timeslot Временной слот доставки */
            'timeslot' => $this->timeslot,

            /** @var array $customer Информация о покупателе */
            'customer' => $this->when(!empty($this->customer), [
                'name' => $this->customer->name ?? null,
                'phone' => $this->customer->phone ?? null,
                'email' => $this->customer->email ?? null,
            ]),

            /** @var array $delivery Информация о доставке */
            'delivery' => $this->when(!empty($this->delivery), [
                'address' => $this->delivery->address ?? null,
                'delivery_type' => $this->delivery->delivery_type ?? null,
                'warehouse' => $this->delivery->warehouse ?? null,
                'tracking_number' => $this->delivery->tracking_number ?? null,
                'delivery_service' => $this->delivery->delivery_service ?? null,
            ]),

            /** @var array $items Позиции заказа */
            'items' => collect($this->items ?? [])->map(function ($item) {
                return [
                    'id' => $item->id ?? null,
                    'product_id' => $item->product_id ?? null,
                    'sku' => $item->sku ?? null,
                    'article' => $item->article ?? null,
                    'name' => $item->name ?? null,
                    'quantity' => (int) ($item->quantity ?? 0),
                    'price' => (string) ($item->price ?? 0),
                    'commission' => (string) ($item->commission ?? 0),
                    'country_iso2' => $item->country_iso2 ?? null,
                    'barcode' => $item->barcode ?? null,
                    'weight' => $item->weight ?? null,
                    'dimensions' => $this->when(!empty($item->dimensions), [
                        'length' => $item->dimensions->length ?? null,
                        'width' => $item->dimensions->width ?? null,
                        'height' => $item->dimensions->height ?? null,
                    ]),
                ];
            })->toArray(),

            /** @var array|null $analytics Аналитические данные */
            'analytics' => $this->when(!empty($this->analytics), [
                'region' => $this->analytics->region ?? null,
                'city' => $this->analytics->city ?? null,
                'delivery_schema' => $this->analytics->delivery_schema ?? null,
                'warehouse_id' => $this->analytics->warehouse_id ?? null,
            ]),
        ];
    }
}
