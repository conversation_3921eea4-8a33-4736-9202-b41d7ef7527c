<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBO;

use App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\OzonOrderItemResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBOIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->integration_id,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->warehouse_id,
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->currency_id,
            /** @var string $customer_order_id Идентификатор заказа в нашей системе */
            'customer_order_id' => $this->customer_order_id,
            /** @var string $status Статус заказа */
            'status' => $this->status,
            /** @var string $number Номер заказа в нашей системе */
            'number' => $this->number,
            /** @var string $posting_number Номер отправления в Ozon */
            'posting_number' => $this->posting_number,
            /** @var string $order_id ID заказа в Ozon */
            'order_id' => $this->order_id,
            /** @var string $order_number Номер заказа в Ozon */
            'order_number' => $this->order_number,
            /** @var string $total_price Общая сумма заказа (строка для BcMath) */
            'total_price' => $this->total_price,
            /** @var bool $reserve Признак резервирования */
            'reserve' => $this->reserve,
            /** @var string $in_process_at Дата и время обработки заказа */
            'in_process_at' => $this->in_process_at,
            /** @var string $created_at_ozon Дата и время создания заказа в Ozon */
            'created_at_ozon' => $this->created_at_ozon,
            /** @var bool $has_unmatched_items Признак наличия несопоставленных товаров */
            'has_unmatched_items' => $this->has_unmatched_items,
            /** @var bool $needs_warehouse_mapping Признак необходимости сопоставления склада */
            'needs_warehouse_mapping' => $this->needs_warehouse_mapping,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $deleted_at Дата и время удаления записи */
            'deleted_at' => $this->deleted_at,

            'items' => $this->when($this->items, function () {
                return OzonOrderItemResource::collection($this->items);
            }),
        ];
    }
}
