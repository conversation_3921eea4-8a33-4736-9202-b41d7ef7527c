<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Entities\BaseEntity;
use Illuminate\Http\Request;

readonly class ValidSortFieldRule implements ValidationRule
{
    public function __construct(
        private BaseEntity $entity,
        private Request $request
    ) {
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$value) {
            return;
        }

        if (str_contains($value, '.')) {
            $selectedFields = collect($this->request->input('fields', []))
                ->flatMap(fn ($field) => explode(',', $field))
                ->map(fn ($field) => trim($field))
                ->unique()
                ->values();

            if (!$selectedFields->contains($value) && !$selectedFields->contains(explode('.', $value)[0])) {
                $fail("The sort field '{$value}' must be included in the selected fields or its relation must be requested.");
            }
        } elseif (!in_array($value, $this->entity::getFields(), true)) {
            $fail("The sort field '{$value}' is not a valid field.");
        }
    }
}
