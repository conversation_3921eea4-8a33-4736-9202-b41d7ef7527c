<?php

namespace App\Traits;

use App\Contracts\DtoContract;
use App\Contracts\Policies\BaseResourcePolicyContract;
use Illuminate\Http\Request;

trait ApiPolicy
{
    use ApiResponse;

    protected function authorizeView(Request $request, string $id): void
    {
        $this->getPolicy()->view($request->user(), $id);
    }

    protected function authorizeCreate(Request $request, DtoContract $dto): void
    {
        $this->getPolicy()->create($request->user(), $dto);
    }

    protected function authorizeUpdate(Request $request, DtoContract $dto): void
    {
        $this->getPolicy()->update($request->user(), $dto);
    }

    protected function authorizeDelete(Request $request, string $id): void
    {
        $this->getPolicy()->delete($request->user(), $id);
    }

    abstract protected function getPolicy(): BaseResourcePolicyContract;
}
