<?php

namespace App\Traits;

trait ArrayInsertAfterKey
{
    /**
     * Вставляет массив после указанного ключа в другой массив.
     * Inserts an array after a specified key in another array.
     *
     * This function searches for the specified key in the provided array. If the key is found,
     * it inserts the given array immediately after that key. If the key is not found, the given
     * array is appended to the end of the original array.
     *
     * @param array   $array         The original array to modify.
     * @param mixed   $key           The key after which the new array should be inserted.
     * @param array   $insert_array  The array to insert into the original array.
     *
     * @return void
     */
    public function array_insert_after_key(& $array, $key, $insert_array): void
    {

        $index = array_search($key, array_keys($array));

        // key is not found, add to the end of the array
        if ($index === false) {
            $array = array_merge($array, $insert_array);
        }
        // split the array into two parts and insert a new element between them
        else {
            $array = array_merge(
                array_slice($array, 0, $index + 1, true),
                $insert_array,
                array_slice($array, $index + 1, null, true)
            );
        }
    }
}
