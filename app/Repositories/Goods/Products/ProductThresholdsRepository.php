<?php

namespace App\Repositories\Goods\Products;

use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProductThresholdsRepository implements ProductThresholdsRepositoryContract
{
    private const TABLE = 'product_threshold_warehouses';

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE)
        ->where('id', $id)
        ->get();
    }

    public function getMyCountWhereInIds(array|Collection $ids, string $cabinetId): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->where('cabinet_id', $cabinetId)
            ->count();
    }

    public function getWhereProductId(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE)
            ->where('product_id', $resourceId)
            ->get();
    }


    public function getFirst(string $resourceId): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $resourceId)
            ->first();
    }


    public function oldProductThresholdIdsForProducts(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE) // текущие
            ->where('product_id', $resourceId)
            ->pluck('id');
    }

    public function upsert(Collection $data): int
    {
        return DB::table(self::TABLE)->upsert(
            $data->toArray(),
            ['id'],
            [
                'product_id',
                // 'cabinet_id',
                // 'type',
                'warehouse_id',
                'threshold_count',
            ]
        );
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => now()]
                )
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)->where('id', $id)->delete();
    }

    public function deleteArray(array|Collection $id): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->get();
    }

}
