<?php

namespace App\Repositories\Procurement\Acceptances;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Entities\AcceptanceItemEntity;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AcceptanceItemsRepository implements AcceptanceItemsRepositoryContract
{
    use HasTimestamps;
    private const TABLE = 'acceptance_items';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function checkItemExists(string $productId, string $acceptanceId): bool
    {
        return DB::table(self::TABLE)
            ->where('product_id', $productId)
            ->where('acceptance_id', $acceptanceId)
            ->exists();
    }
    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(array_merge(
                $data,
                ['updated_at' => Carbon::now()]
            ));
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $entity = new AcceptanceItemEntity();
        [$baseFields, $relationFields] = $entity->parseFields($fields);

        $query = $entity;
        $query->select($baseFields)
            ->where('acceptance_id', $id);

        $query->addSelect([
            DB::raw('(
                SELECT COALESCE(SUM(wi.quantity) - COALESCE(SUM(swi.quantity), 0), 0)
                FROM warehouse_items wi
                JOIN acceptances a_current ON a_current.id = acceptance_items.acceptance_id
                LEFT JOIN shipment_warehouse_items swi ON wi.id = swi.warehouse_item_id
                WHERE wi.product_id = acceptance_items.product_id
                  AND wi.warehouse_id = a_current.warehouse_id
                  AND wi.acceptance_id != acceptance_items.acceptance_id
                  AND wi.received_at <= a_current.date_from
                  AND wi.status != \'out_of_stock\'
            ) as recidual')
        ]);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->whereHas('products', function ($query) use ($filters) {
                $query->where('title', 'ILIKE', '%'.$filters['search']['value'].'%');
            });
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function whereIn(string $field, array $resourceIds): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn($field, $resourceIds)
            ->get();
    }

    public function bulkDelete(array $ids): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $ids)
            ->delete();
    }

    public function getByAcceptanceId(string $acceptanceId): Collection
    {
        return DB::table(self::TABLE)
            ->where('acceptance_id', $acceptanceId)
            ->get();
    }

    public function getWithoutMeta(string $acceptanceId): Collection
    {
        return DB::table(self::TABLE)
            ->where('acceptance_id', $acceptanceId)
            ->get();
    }

    /**
     * Рассчитывает показатели для еще не созданной позиции приемки
     *
     * @param string $productId ID товара
     * @param string $warehouseId ID склада
     * @param string $dateFrom Дата приемки
     * @param string $cabinetId ID кабинета
     * @return object Объект с рассчитанными показателями
     */
    public function calculateMetricsForNewItem(string $productId, string $warehouseId, string $dateFrom, string $cabinetId): object
    {
        $result = DB::selectOne("
            SELECT
                COALESCE(SUM(wi.quantity) - COALESCE(SUM(swi.quantity), 0), 0) as recidual
            FROM warehouse_items wi
            LEFT JOIN shipment_warehouse_items swi ON wi.id = swi.warehouse_item_id
            LEFT JOIN shipment_items si ON swi.shipment_item_id = si.id
            LEFT JOIN shipments s ON si.shipment_id = s.id
            WHERE wi.product_id = ?
              AND wi.warehouse_id = ?
              AND wi.received_at <= ?
              AND wi.status != 'out_of_stock'
              AND (s.date_from < ? OR s.date_from IS NULL)
        ", [$productId, $warehouseId, $dateFrom, $dateFrom]);

        return (object) [
            'recidual' => $result->recidual ?? 0
        ];
    }
}
