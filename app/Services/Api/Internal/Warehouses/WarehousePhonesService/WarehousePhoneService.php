<?php

namespace App\Services\Api\Internal\Warehouses\WarehousePhonesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Warehouses\WarehousePhonesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers\WarehousePhonesCreateHandler;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers\WarehousePhonesDeleteHandler;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers\WarehousePhonesGetHandler;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers\WarehousePhonesShowHandler;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers\WarehousePhonesUpdateHandler;
use Illuminate\Support\Collection;

readonly class WarehousePhoneService implements WarehousePhonesServiceContract
{
    public function __construct(
        private WarehousePhonesCreateHandler $createHandler,
        private WarehousePhonesUpdateHandler $updateHandler,
        private WarehousePhonesGetHandler $getHandler,
        private WarehousePhonesDeleteHandler $deleteHandler,
        private WarehousePhonesShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
