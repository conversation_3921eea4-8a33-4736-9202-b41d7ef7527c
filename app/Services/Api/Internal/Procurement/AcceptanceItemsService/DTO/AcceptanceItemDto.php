<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Services\Api\Internal\Procurement\Acceptances\Traits\AcceptanceTotalCalculator;

class AcceptanceItemDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use AcceptanceTotalCalculator;

    public int $totalPrice;

    public function __construct(
        public int $quantity,
        public ?string $acceptanceId = null,
        public ?string $productId = null,
        public int $price = 0,
        public int $discount = 0,
        public ?string $vat_rate_id = null,
        public ?string $countryId = null,
        public ?string $gtdNumber = null,
        public ?string $resourceId = null
    ) {
        $this->totalPrice = $this->calculateTotalPrice();
    }

    /**
     * Рассчитывает total_price с учетом настроек НДС в приемке
     */
    private function calculateTotalPrice(): int
    {
        if (!$this->acceptanceId) {
            // Если нет ID приемки, используем простой расчет
            return $this->price * $this->quantity;
        }

        $vatInfo = $this->getVatInfo($this->acceptanceId, $this->vat_rate_id);

        $totalPrice = $this->calculateAcceptanceItemTotal(
            (string)$this->price,
            (string)$this->quantity,
            (string)$this->discount,
            $vatInfo['vat_rate'],
            $vatInfo['price_includes_vat'],
            $vatInfo['has_vat']
        );

        return (int)round((string)$totalPrice);
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'acceptance_id' => $this->acceptanceId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'total_price' => $this->totalPrice,
            'country_id' => $this->countryId,
            'gtd_number' => $this->gtdNumber,
            ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'total_price' => $this->totalPrice,
            'country_id' => $this->countryId,
            'gtd_number' => $this->gtdNumber,
        ];
    }

    public static function fromArray(array $data): self
    {
        $instance = new self(
            quantity: $data['quantity'],
            acceptanceId: $data['acceptance_id'] ?? null,
            productId: $data['product_id'] ?? null,
            price: $data['price'] ?? 0,
            discount: $data['discount'] ?? 0,
            vat_rate_id: $data['vat_rate_id'] ?? null,
            countryId: $data['country_id'] ?? null,
            gtdNumber: $data['gtd_number'] ?? null,
            resourceId: $data['id'] ?? null,
        );

        // Пересчитываем totalPrice после создания экземпляра
        $instance->totalPrice = $instance->calculateTotalPrice();

        return $instance;
    }
}
