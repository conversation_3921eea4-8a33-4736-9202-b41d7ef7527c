<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService;

use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Services\Api\Internal\Procurement\Acceptances\Traits\AcceptanceTotalCalculator;
use App\Traits\PrecisionCalculator;

/**
 * Сервис для расчета НДС в позициях приемки с учетом настроек документа
 */
class AcceptanceItemVatCalculatorService
{
    use PrecisionCalculator;
    use AcceptanceTotalCalculator;

    public function __construct(
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {
    }

    /**
     * Рассчитывает детальную информацию о НДС для позиции приемки
     *
     * @param string $acceptanceId ID приемки
     * @param int $price Цена за единицу (в копейках)
     * @param int $quantity Количество
     * @param int $discount Скидка в процентах
     * @param string|null $vatRateId ID ставки НДС
     * @return array Детальная информация о расчете НДС
     */
    public function calculateItemVatDetails(
        string $acceptanceId,
        int $price,
        int $quantity,
        int $discount,
        ?string $vatRateId = null
    ): array {
        $acceptance = $this->acceptanceRepository->show($acceptanceId);

        if (!$acceptance) {
            throw new \RuntimeException('Acceptance not found');
        }

        // Получаем ставку НДС
        $vatRate = '0';
        if ($vatRateId && $acceptance->has_vat) {
            $vat = $this->vatRatesRepository->show($vatRateId);
            $vatRate = $vat ? (string)$vat->rate : '0';
        }

        // Базовая сумма = цена × количество
        $baseAmount = $this->multiply((string)$price, (string)$quantity);

        // Применяем скидку
        $amountAfterDiscount = $this->applyDiscount($baseAmount, (string)$discount);

        // Если в документе нет НДС или ставка НДС = 0
        if (!$acceptance->has_vat || $this->equals($vatRate, '0')) {
            return [
                'base_amount' => $baseAmount,
                'discount_amount' => $this->subtract($baseAmount, $amountAfterDiscount),
                'amount_after_discount' => $amountAfterDiscount,
                'vat_rate' => '0',
                'vat_amount' => '0',
                'total_amount' => $amountAfterDiscount,
                'has_vat' => false,
                'price_includes_vat' => $acceptance->price_includes_vat
            ];
        }

        // Расчет НДС в зависимости от настроек
        if ($acceptance->price_includes_vat) {
            // Цена включает НДС - выделяем НДС
            $vatCalc = $this->extractVat($amountAfterDiscount, $vatRate);

            return [
                'base_amount' => $baseAmount,
                'discount_amount' => $this->subtract($baseAmount, $amountAfterDiscount),
                'amount_after_discount' => $amountAfterDiscount,
                'vat_rate' => $vatRate,
                'vat_amount' => $vatCalc['vat_amount'],
                'amount_without_vat' => $vatCalc['amount_without_vat'],
                'total_amount' => $vatCalc['total_amount'],
                'has_vat' => true,
                'price_includes_vat' => true
            ];
        }

        // Цена не включает НДС - добавляем НДС
        $vatCalc = $this->calculateVat($amountAfterDiscount, $vatRate);

        return [
            'base_amount' => $baseAmount,
            'discount_amount' => $this->subtract($baseAmount, $amountAfterDiscount),
            'amount_after_discount' => $amountAfterDiscount,
            'vat_rate' => $vatRate,
            'vat_amount' => $vatCalc['vat_amount'],
            'amount_without_vat' => $vatCalc['amount_without_vat'],
            'total_amount' => $vatCalc['total_amount'],
            'has_vat' => true,
            'price_includes_vat' => false
        ];
    }

    /**
     * Получает итоговую сумму позиции с учетом всех настроек НДС
     *
     * @param string $acceptanceId ID приемки
     * @param int $price Цена за единицу (в копейках)
     * @param int $quantity Количество
     * @param int $discount Скидка в процентах
     * @param string|null $vatRateId ID ставки НДС
     * @return string Итоговая сумма в копейках
     */
    public function calculateItemTotal(
        string $acceptanceId,
        int $price,
        int $quantity,
        int $discount,
        ?string $vatRateId = null
    ): string {
        $details = $this->calculateItemVatDetails($acceptanceId, $price, $quantity, $discount, $vatRateId);
        return (int)round((string)$details['total_amount']);
    }

    /**
     * Проверяет, нужно ли автоматически назначить ставку "Без НДС"
     *
     * @param string $acceptanceId ID приемки
     * @param string|null $vatRateId Текущая ставка НДС
     * @return string|null ID ставки "Без НДС" если нужно назначить, иначе null
     */
    public function getAutoVatRateId(string $acceptanceId, ?string $vatRateId = null): ?string
    {
        $acceptance = $this->acceptanceRepository->show($acceptanceId);

        if (!$acceptance || $acceptance->has_vat || $vatRateId) {
            return null;
        }

        // Если has_vat = false и vat_rate_id не указан, ищем ставку "Без НДС"
        $noVatRate = $this->vatRatesRepository->getByRateAndCabinet(0, $acceptance->cabinet_id);

        return $noVatRate?->id;
    }
}
