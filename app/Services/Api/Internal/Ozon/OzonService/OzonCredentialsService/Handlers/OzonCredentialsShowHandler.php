<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers;

use App\Contracts\Repositories\OzonCredentialsRepositoryContract;

class OzonCredentialsShowHandler
{
    public function __construct(
        private readonly OzonCredentialsRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $credential = $this->repository->show($resourceId);

        if ($credential) {
            $credential->client_id = decrypt($credential->client_id);
            $credential->api_key = decrypt($credential->api_key);
        }

        return $credential;
    }
}
