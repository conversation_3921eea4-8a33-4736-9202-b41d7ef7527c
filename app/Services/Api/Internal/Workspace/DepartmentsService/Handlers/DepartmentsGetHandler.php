<?php

namespace App\Services\Api\Internal\Workspace\DepartmentsService\Handlers;

use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class DepartmentsGetHandler
{
    public function __construct(
        private DepartmentsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
