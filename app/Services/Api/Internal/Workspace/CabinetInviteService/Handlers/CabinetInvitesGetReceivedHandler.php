<?php

namespace App\Services\Api\Internal\Workspace\CabinetInviteService\Handlers;

use App\Contracts\Repositories\CabinetInvitesRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

readonly class CabinetInvitesGetReceivedHandler
{
    public function __construct(
        private CabinetInvitesRepositoryContract $repository
    ) {
    }

    public function run(): Collection
    {
        $user = Auth::user();
        return $this->repository->getAllUserInvites($user->email);
    }
}
