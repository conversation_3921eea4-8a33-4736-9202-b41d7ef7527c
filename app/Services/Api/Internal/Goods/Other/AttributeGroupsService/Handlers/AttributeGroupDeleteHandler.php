<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeGroupsService\Handlers;

use App\Contracts\Repositories\AttributeGroupsRepositoryContract;
use App\Traits\HasOrderedUuid;

class AttributeGroupDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly AttributeGroupsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
