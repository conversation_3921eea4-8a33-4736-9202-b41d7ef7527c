<?php

namespace App\Services\Api\Internal\Goods\Products\ProductAccountingFeaturesService\Handlers;

use App\Contracts\Repositories\ProductAccountingFeaturesRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductAccountingFeatureGetAllHandler
{
    public function __construct(
        private ProductAccountingFeaturesRepositoryContract $productAccountingFeaturesRepository,
    ) {
    }

    public function run(string $resourceId): Collection
    {
        return $this->productAccountingFeaturesRepository->getFirstForProduct($resourceId);
    }
}
