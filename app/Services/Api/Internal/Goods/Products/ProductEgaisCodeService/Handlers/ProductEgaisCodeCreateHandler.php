<?php

namespace App\Services\Api\Internal\Goods\Products\ProductEgaisCodeService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ProductEgaisCodeRepositoryContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;

class ProductEgaisCodeCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly ProductEgaisCodeRepositoryContract $productEgaisCodeRepository,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {

        $this->productEgaisCodeRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        return $this->resourceId;
    }
}
