<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\Handlers;

use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;

class CustomerOrdersShowHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public function __construct(
        private readonly CustomerOrdersRepositoryContract $customerOrdersRepository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $data = $this->customerOrdersRepository->show($resourceId);

        $data->delivery_info = json_decode($data->delivery_info, true);
        $data->files = json_decode($data->files, true);
        $data->status = json_decode($data->status, true);
        $data->contractor = json_decode($data->contractor, true);
        $data->warehouse = json_decode($data->warehouse, true);

        if ($data->files) {
            $this->generateUrls($data->files);
        }
        return $data;
    }
}
