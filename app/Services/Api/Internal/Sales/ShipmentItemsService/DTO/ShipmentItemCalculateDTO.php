<?php

namespace App\Services\Api\Internal\Sales\ShipmentItemsService\DTO;

use App\Contracts\DtoContract;

class ShipmentItemCalculateDTO implements DtoContract
{
    public function __construct(
        public string $productId,
        public string $warehouseId,
        public string $dateFrom,
        public string $cabinetId
    ) {}

    public static function fromArray(array $data): self
    {
        return new self(
            productId: $data['product_id'],
            warehouseId: $data['warehouse_id'],
            dateFrom: $data['date_from'],
            cabinetId: $data['cabinet_id']
        );
    }
}
