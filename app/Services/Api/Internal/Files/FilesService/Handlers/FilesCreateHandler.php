<?php

namespace App\Services\Api\Internal\Files\FilesService\Handlers;

use App\Contracts\DtoContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\FilesRepositoryContract;
use App\Exceptions\NotFoundException;
use App\Services\Storage\S3StorageService;
use App\Traits\HasOrderedUuid;

readonly class FilesCreateHandler
{
    use HasOrderedUuid;
    private string $resourceId;
    public function __construct(
        private FilesRepositoryContract $repository,
        private S3StorageService $storageService,
        private EmployeeRepositoryContract $employeeRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(DtoContract $dto): string
    {
        $file = $dto->file;
        $employee = $this->employeeRepository->getByUserIdAndCabinet(auth()->id(), $dto->cabinetId);

        if (!$employee) {
            throw new NotFoundException('Employee not found');
        }
        // Сохраняем файл в S3
        $finalPath = $this->storageService->store($file, $dto->isPrivate);

        // Создаем запись в БД
        $this->repository->insert([
            'id' => $this->resourceId,
            'name' => $file->getClientOriginalName(),
            'path' => $finalPath,
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'is_private' => $dto->isPrivate,
            'type' => $dto->type,
            'employee_id' => $employee->id,
            'cabinet_id' => $dto->cabinetId
        ]);

        return $this->resourceId;
    }
}
