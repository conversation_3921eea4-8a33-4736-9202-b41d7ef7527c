<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\MeasurementUnitsRepositoryContract;
use App\Contracts\Services\Internal\Directories\MeasurementUnitGroupServiceContract;
use App\Services\Api\Internal\References\LegalEntitiesService\Traits\LegalEntitiesManages;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\DTO\MeasurementUnitDTO;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class MeasurementUnitUpdateHandler
{
    use HasOrderedUuid;
    use LegalEntitiesManages;

    public string $resourceId;

    public function __construct(
        private readonly MeasurementUnitsRepositoryContract $repository,
        private readonly MeasurementUnitGroupServiceContract $groupService
    ) {
    }

    /**
     * @throws Exception
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof MeasurementUnitDTO) {
            throw new InvalidArgumentException('DTO must be instance of MeasurementUnitDTO');
        }

        DB::beginTransaction();

        //Получаем ресурс и его группу
        $updatedUnit = $this->repository->findById(
            $dto->resourceId,
            $dto->cabinetId,
            [
                'mu.*',
                'mug.is_system as is_system_group',
                'mug.name as group_name',
                'mug.tech_type as group_tech_type'
            ]
        );


        //Собираем объект группы, может понадобиться при клонировании или конвертации базовой единицы
        $group = (object) [
            'id' => $updatedUnit->group_id,
            'name' => $updatedUnit->group_name,
            'is_system' => $updatedUnit->is_system_group,
            'tech_type' => $updatedUnit->group_tech_type
        ];
        $dto->groupId = $group->id;


        //Если группа системная - клонируем ее и дальше будем отображать только пользовательскую
        if ($group->is_system) {
            $cloneGroup = $this->groupService->cloneSystemGroup($group, $dto->cabinetId, $dto->resourceId);


            $dto->resourceId = $cloneGroup['new_resource_id'];
            $dto->groupId = $cloneGroup['group_id'];
        }

        $date = Carbon::now();
        try {

            //Если ресурс является базовым - ничего делать не надо
            if ($dto->conversionFactor == 1 && $updatedUnit->conversion_factor != 1) {

                //Получаем все единицы группы
                $units = $this->repository->getUnitsByGroup($dto->groupId);

                //Проверяем что у нас в группе уже есть базовый юнит, иначе мы не можем провести конвертацию факторов
                if ($units->where('conversion_factor', 1)->count() == 1) {
                    $newBaseFactor = $updatedUnit->conversion_factor;

                    foreach ($units as $unit) {
                        if ($unit->id != $dto->resourceId) {
                            // Новый коэффициент = (Старый коэффициент) * (Старый базовый фактор / Новый базовый фактор)
                            $newConversionFactor = $unit->conversion_factor * (1 / $newBaseFactor);
                            $this->repository->update(
                                $unit->id,
                                [
                                    'conversion_factor' => $newConversionFactor,
                                    'updated_at' => $date
                                ]
                            );
                        }
                    }
                }
            }

            $this->repository->update(
                $dto->resourceId,
                $dto->toUpdateArray(),
            );


            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
