<?php

namespace App\Services\Api\Internal\References\DiscountsService\Handlers;

use App\Contracts\Repositories\DiscountsRepositoryContract;
use App\Enums\Api\Internal\DiscountTypeEnum;
use App\Traits\HasOrderedUuid;

class DiscountsShowHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly DiscountsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $result = $this->repository->show($resourceId);
        $result->savings = json_decode($result->savings, true);
        $result->products = json_decode($result->products, true);
        $result->contractor_groups = json_decode($result->contractor_groups, true);

        $result->type_name = $result->type
            ? DiscountTypeEnum::from($result->type)->getType()
            : null;

        return $result;
    }
}
