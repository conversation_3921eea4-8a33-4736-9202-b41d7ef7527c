<?php

namespace App\Services\Api\Internal\Money\PaymentsService\Handlers;

use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;
use App\Contracts\Repositories\OutgoingPaymentsRepositoryContract;

readonly class PaymentBulkHeldHandler
{
    public function __construct(
        private IncomingPaymentsRepositoryContract $incomingPaymentsRepository,
        private OutgoingPaymentsRepositoryContract $outgoingPaymentsRepository
    ) {
    }

    public function run(array $ids): void
    {
        $this->incomingPaymentsRepository->updateWhereInIds($ids, ['held' => true]);
        $this->outgoingPaymentsRepository->updateWhereInIds($ids, ['held' => true]);
    }
}
