<?php

require_once 'vendor/autoload.php';

use App\Entities\AcceptanceItemEntity;
use App\Extensions\Scramble\FormRequestExtension;
use App\Rules\AllowedFieldsRule;

// Create entity instance
$entity = new AcceptanceItemEntity();

// Create extension instance (we'll need to mock dependencies)
try {
    // Test the getAllowedFields method directly
    $allowedFields = $entity->getAllowedFields();
    
    echo "All allowed fields:\n";
    foreach ($allowedFields as $field) {
        echo "- $field\n";
    }
    
    echo "\nTotal fields: " . count($allowedFields) . "\n";
    
    // Test structured fields
    $reflection = new ReflectionClass(FormRequestExtension::class);
    $method = $reflection->getMethod('getStructuredAllowedFields');
    $method->setAccessible(true);
    
    // We need to create a mock instance
    $extension = new FormRequestExtension(
        app(\Dedoc\Scramble\Infer::class),
        app(\Dedoc\Scramble\Support\Generator\TypeTransformer::class),
        app(\Dedoc\Scramble\GeneratorConfig::class)
    );
    
    $structuredFields = $method->invoke($extension, $entity);
    
    echo "\nStructured fields:\n";
    foreach ($structuredFields as $field) {
        echo "- $field\n";
    }
    
    echo "\nTotal structured fields: " . count($structuredFields) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
